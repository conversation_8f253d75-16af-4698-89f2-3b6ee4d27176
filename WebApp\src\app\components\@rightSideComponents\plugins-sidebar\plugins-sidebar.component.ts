import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService } from '../../../../shared/services/theam.service';
import { PluginResponseDto } from '../../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-plugins-sidebar',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './plugins-sidebar.component.html',
  styleUrls: ['./plugins-sidebar.component.css']
})
export class PluginsSidebarComponent {
  // Inject ThemeService
  themeService = inject(ThemeService);

  // Input properties
  @Input() isPluginSidebarOpen: boolean = false;
  @Input() pluginSidebarTitle: string = 'Agent Plugins';
  @Input() agentPlugins: PluginResponseDto[] = [];
  @Input() selectedAgent: string = '';

  // Output events
  @Output() onClose = new EventEmitter<void>();
  @Output() onSelectPlugin = new EventEmitter<PluginResponseDto>();

  /**
   * Getter for showSidebar to match source-references component property name
   * This allows for consistent template usage
   */
  get showSidebar(): boolean {
    return this.isPluginSidebarOpen;
  }

  /**
   * Handles the sidebar close event
   */
  closeSidebar(): void {
    this.onClose.emit();
  }

  /**
   * Alternative method name to match source-references component
   */
  onCloseSidebar(): void {
    this.closeSidebar();
  }

  /**
   * Handles plugin selection
   * @param plugin The selected plugin
   */
  selectPlugin(plugin: PluginResponseDto): void {
    if (!plugin) return;
    this.onSelectPlugin.emit(plugin);
  }

  /**
   * Gets the plugin type color for display
   * @param type The plugin type
   * @returns The color class for the plugin type
   */
  getPluginTypeColor(type: string | undefined): string {
    if (!type) return 'default';
    switch (type.toLowerCase()) {
      case 'openapi':
        return 'blue';
      case 'customplugin':
        return 'green';
      default:
        return 'default';
    }
  }

  /**
   * Gets the plugin type badge class
   * @param type The plugin type
   * @returns The CSS class for the badge
   */
  getPluginTypeBadgeClass(type: string | undefined): string {
    if (!type) return '';
    switch (type.toLowerCase()) {
      case 'openapi':
        return this.themeService.isDarkMode()
          ? 'bg-blue-900 text-blue-300'
          : 'bg-blue-100 text-blue-600';
      case 'customplugin':
        return this.themeService.isDarkMode()
          ? 'bg-green-900 text-green-300'
          : 'bg-green-100 text-green-600';
      default:
        return this.themeService.isDarkMode()
          ? 'bg-gray-700 text-gray-300'
          : 'bg-gray-100 text-gray-600';
    }
  }

  /**
   * Gets the function list from the functions string
   * @param functions The functions string
   * @returns Array of function names
   */
  getFunctionList(functions: string | undefined): string[] {
    if (!functions) return [];
    try {
      const parsed = JSON.parse(functions);
      if (Array.isArray(parsed)) {
        return parsed.map(func => func.name || func).filter(Boolean);
      }
      return [];
    } catch {
      // If not JSON, try to split by common delimiters
      return functions.split(/[,;\n]/).map(f => f.trim()).filter(Boolean);
    }
  }

  /**
   * Gets a truncated description for the plugin
   * @param functions The functions string to extract description from
   * @returns A short description
   */
  getPluginDescription(functions: string | undefined): string {
    if (!functions) return 'No functions available';

    const functionList = this.getFunctionList(functions);
    if (functionList.length === 0) return 'No functions available';

    if (functionList.length === 1) {
      return `1 function: ${functionList[0]}`;
    }

    return `${functionList.length} functions available`;
  }
}
