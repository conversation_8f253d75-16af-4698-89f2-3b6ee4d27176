import { Component, Input, Output, EventEmitter, inject, OnChanges, SimpleChanges, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService } from '../../../../shared/services/theam.service';
import { PluginResponseDto, PluginServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { DateTime } from 'luxon';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-plugins-sidebar',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './plugins-sidebar.component.html',
  styleUrls: ['./plugins-sidebar.component.css']
})
export class PluginsSidebarComponent implements OnChanges, OnDestroy {
  // Inject services
  themeService = inject(ThemeService);
  private pluginService = inject(PluginServiceProxy);

  // Input properties
  @Input() isPluginSidebarOpen: boolean = false;
  @Input() pluginSidebarTitle: string = 'Agent Plugins';
  @Input() selectedAgent: string = '';

  // Output events
  @Output() onClose = new EventEmitter<void>();
  @Output() onSelectPlugin = new EventEmitter<PluginResponseDto>();

  // Internal state
  agentPlugins: PluginResponseDto[] = [];
  isLoading: boolean = false;
  hasError: boolean = false;
  errorMessage: string = '';

  // Subscription management
  private pluginSubscription?: Subscription;

  /**
   * Lifecycle hook - responds to input property changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    // Load plugins when selectedAgent changes
    if (changes['selectedAgent'] && changes['selectedAgent'].currentValue) {
      const agentName = changes['selectedAgent'].currentValue;
      if (agentName && agentName !== changes['selectedAgent'].previousValue) {
        this.loadPlugins(agentName);
      }
    }
  }

  /**
   * Lifecycle hook - cleanup subscriptions
   */
  ngOnDestroy(): void {
    if (this.pluginSubscription) {
      this.pluginSubscription.unsubscribe();
    }
  }

  /**
   * Getter for showSidebar to match source-references component property name
   * This allows for consistent template usage
   */
  get showSidebar(): boolean {
    return this.isPluginSidebarOpen;
  }

  /**
   * Handles the sidebar close event
   */
  closeSidebar(): void {
    this.onClose.emit();
  }

  /**
   * Alternative method name to match source-references component
   */
  onCloseSidebar(): void {
    this.closeSidebar();
  }

  /**
   * Loads plugins for the specified agent
   * @param agentName The name of the agent to load plugins for
   */
  loadPlugins(agentName: string): void {
    if (!agentName) {
      console.warn('No agent name provided for loading plugins');
      this.resetPluginState();
      return;
    }

    console.log('Loading plugins for agent:', agentName);
    this.isLoading = true;
    this.hasError = false;
    this.errorMessage = '';

    // Clean up previous subscription
    if (this.pluginSubscription) {
      this.pluginSubscription.unsubscribe();
    }

    // Load plugins from API
    this.pluginSubscription = this.pluginService.getByAgentName(agentName).subscribe({
      next: (plugins) => {
        console.log('Loaded plugins for agent:', agentName, plugins);
        this.agentPlugins = plugins || [];
        this.updateSidebarTitle(agentName);
        this.isLoading = false;
        this.hasError = false;

        // Add mock data for demonstration if no plugins are returned
        if (this.agentPlugins.length === 0) {
          this.addMockPluginData(agentName);
        }
      },
      error: (error) => {
        console.error('Error loading plugins for agent:', error);
        this.hasError = true;
        this.errorMessage = `Failed to load plugins for ${agentName}`;
        this.isLoading = false;

        // Add mock data for demonstration on error
        this.addMockPluginData(agentName);
      },
    });
  }

  /**
   * Resets the plugin state to initial values
   */
  private resetPluginState(): void {
    this.agentPlugins = [];
    this.isLoading = false;
    this.hasError = false;
    this.errorMessage = '';
    this.pluginSidebarTitle = 'Agent Plugins';
  }

  /**
   * Updates the sidebar title with plugin count
   * @param agentName The agent name
   */
  private updateSidebarTitle(agentName: string): void {
    this.pluginSidebarTitle = `${agentName} Plugins (${this.agentPlugins.length})`;
  }

  /**
   * Handles plugin selection
   * @param plugin The selected plugin
   */
  selectPlugin(plugin: PluginResponseDto): void {
    if (!plugin) return;
    this.onSelectPlugin.emit(plugin);
  }

  /**
   * Gets the plugin type color for display
   * @param type The plugin type
   * @returns The color class for the plugin type
   */
  getPluginTypeColor(type: string | undefined): string {
    if (!type) return 'default';
    switch (type.toLowerCase()) {
      case 'openapi':
        return 'blue';
      case 'customplugin':
        return 'green';
      default:
        return 'default';
    }
  }

  /**
   * Gets the plugin type badge class
   * @param type The plugin type
   * @returns The CSS class for the badge
   */
  getPluginTypeBadgeClass(type: string | undefined): string {
    if (!type) return '';
    switch (type.toLowerCase()) {
      case 'openapi':
        return this.themeService.isDarkMode()
          ? 'bg-blue-900 text-blue-300'
          : 'bg-blue-100 text-blue-600';
      case 'customplugin':
        return this.themeService.isDarkMode()
          ? 'bg-green-900 text-green-300'
          : 'bg-green-100 text-green-600';
      default:
        return this.themeService.isDarkMode()
          ? 'bg-gray-700 text-gray-300'
          : 'bg-gray-100 text-gray-600';
    }
  }

  /**
   * Gets the function list from the functions string
   * @param functions The functions string
   * @returns Array of function names
   */
  getFunctionList(functions: string | undefined): string[] {
    if (!functions) return [];
    try {
      const parsed = JSON.parse(functions);
      if (Array.isArray(parsed)) {
        return parsed.map(func => func.name || func).filter(Boolean);
      }
      return [];
    } catch {
      // If not JSON, try to split by common delimiters
      return functions.split(/[,;\n]/).map(f => f.trim()).filter(Boolean);
    }
  }

  /**
   * Gets a truncated description for the plugin
   * @param functions The functions string to extract description from
   * @returns A short description
   */
  getPluginDescription(functions: string | undefined): string {
    if (!functions) return 'No functions available';

    const functionList = this.getFunctionList(functions);
    if (functionList.length === 0) return 'No functions available';

    if (functionList.length === 1) {
      return `1 function: ${functionList[0]}`;
    }

    return `${functionList.length} functions available`;
  }

  /**
   * Adds mock plugin data for demonstration purposes
   * This should be removed in production when real data is available
   * @param agentName The agent name to use in mock data
   */
  private addMockPluginData(agentName: string): void {
    console.log('Adding mock plugin data for agent:', agentName);

    // Create mock plugin data for demonstration
    this.agentPlugins = [
      new PluginResponseDto({
        id: 'mock-1',
        pluginName: 'Web Search Plugin',
        type: 'OpenAPI',
        functions: JSON.stringify([
          { name: 'searchWeb', description: 'Search the web for information' },
          { name: 'getWebPage', description: 'Retrieve content from a web page' },
          { name: 'extractContent', description: 'Extract content from web pages' }
        ]),
        url: 'https://api.websearch.example.com',
        createdDate: DateTime.now(),
        lastModifiedDate: DateTime.now(),
        requiredParameters: 'apiKey',
        environmentVariables: 'SEARCH_API_KEY'
      }),
      new PluginResponseDto({
        id: 'mock-2',
        pluginName: 'Email Generator',
        type: 'CustomPlugin',
        functions: JSON.stringify([
          { name: 'generateEmail', description: 'Generate professional emails' },
          { name: 'validateEmail', description: 'Validate email addresses' },
          { name: 'formatEmail', description: 'Format email content' },
          { name: 'sendEmail', description: 'Send emails via SMTP' }
        ]),
        url: '',
        createdDate: DateTime.now(),
        lastModifiedDate: DateTime.now(),
        requiredParameters: '',
        environmentVariables: ''
      }),
      new PluginResponseDto({
        id: 'mock-3',
        pluginName: 'Database Query Tool',
        type: 'OpenAPI',
        functions: JSON.stringify([
          { name: 'executeQuery', description: 'Execute SQL queries' },
          { name: 'getTableSchema', description: 'Get database table schema' },
          { name: 'optimizeQuery', description: 'Optimize SQL query performance' }
        ]),
        url: 'https://api.database.example.com',
        createdDate: DateTime.now(),
        lastModifiedDate: DateTime.now(),
        requiredParameters: 'connectionString',
        environmentVariables: 'DB_CONNECTION_STRING'
      })
    ];

    // Update the sidebar title with the new count
    this.updateSidebarTitle(agentName);
    console.log('Mock plugin data added:', this.agentPlugins);
  }
}
