/* Plugin Sidebar Styles */
.plugin-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.plugin-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Dark theme hover effects */
:host-context(.dark-theme) .plugin-card:hover {
  box-shadow: 0 8px 25px rgba(0, 195, 154, 0.2);
}

/* Light theme hover effects */
:host-context(:not(.dark-theme)) .plugin-card:hover {
  box-shadow: 0 8px 25px rgba(107, 70, 193, 0.15);
}

/* Plugin name styling */
.plugin-name {
  font-weight: 600;
  line-height: 1.2;
}

/* Smooth transitions for all interactive elements */
.plugin-card * {
  transition: all 0.2s ease-in-out;
}

/* Custom scrollbar for the sidebar */
:host {
  scrollbar-width: thin;
  scrollbar-color: var(--hover-blue-gray) transparent;
}

:host::-webkit-scrollbar {
  width: 6px;
}

:host::-webkit-scrollbar-track {
  background: transparent;
}

:host::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
  border-radius: 3px;
}

:host::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary-purple);
}

/* Dark theme scrollbar */
:host-context(.dark-theme)::-webkit-scrollbar-thumb {
  background-color: #4a4a55;
}

:host-context(.dark-theme)::-webkit-scrollbar-thumb:hover {
  background-color: #00c39a;
}

/* Backdrop blur effect for sticky header */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Truncate utility */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Enhanced hover effects for plugin cards */
.plugin-card .group:hover .transform {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth scale animation */
.hover\:scale-\[1\.02\]:hover {
  transform: scale(1.02);
}

/* Plugin type badge animations */
.plugin-card span {
  transition: all 0.2s ease-in-out;
}

/* Function tag hover effects */
.plugin-card .flex-wrap span:hover {
  transform: translateY(-1px);
}

/* Dark theme specific styles */
:host-context(.dark-theme) {
  color: white;
}

:host-context(.dark-theme) .plugin-card {
  background: #3a3a45;
  border-color: #4a4a55;
}

:host-context(.dark-theme) .plugin-card:hover {
  border-color: #00c39a;
}

/* Light theme specific styles */
:host-context(:not(.dark-theme)) {
  color: var(--text-dark);
}

:host-context(:not(.dark-theme)) .plugin-card {
  background: var(--background-white);
  border-color: var(--hover-blue-gray);
}

:host-context(:not(.dark-theme)) .plugin-card:hover {
  border-color: var(--primary-purple);
}

/* Animation for sidebar slide-in */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Focus styles for accessibility */
.plugin-card:focus {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

:host-context(.dark-theme) .plugin-card:focus {
  outline-color: #00c39a;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .plugin-card {
    padding: 12px;
  }

  .plugin-card .flex-wrap {
    gap: 4px;
  }

  .plugin-card .flex-wrap span {
    font-size: 10px;
    padding: 2px 6px;
  }
}
