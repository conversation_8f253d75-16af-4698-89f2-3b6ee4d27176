<!-- Agent and Workspace Container -->
<div
  class="agent-workspace-container h-full w-full p-4"
  [ngClass]="{
    'bg-[#2b2b33] text-white': themeService.isDarkMode(),
    'bg-[var(--background-white)] text-[var(--text-dark)]':
      !themeService.isDarkMode()
  }"
>
  <!-- Header with Tabs -->
  <div class="mb-6">
    <h2
      class="text-xl font-bold mb-4"
      [ngClass]="{
        'text-white': themeService.isDarkMode(),
        'text-[var(--text-dark)]': !themeService.isDarkMode()
      }"
    >
      AI Hub
    </h2>

    <!-- Tab Navigation -->
    <div class="flex gap-2 mb-4">
      <button
        (click)="switchTab('agents')"
        class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
        [ngClass]="{
          'bg-[var(--primary-purple)] text-white':
            activeTab === 'agents' && !themeService.isDarkMode(),
          'bg-[#00c39a] text-white':
            activeTab === 'agents' && themeService.isDarkMode(),
          'bg-[var(--hover-blue-gray)] text-[var(--text-dark)]':
            activeTab !== 'agents' && !themeService.isDarkMode(),
          'bg-[#3a3a45] text-gray-300':
            activeTab !== 'agents' && themeService.isDarkMode()
        }"
      >
        Agents
      </button>

      <button
        (click)="switchTab('workspaces')"
        class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
        [ngClass]="{
          'bg-[var(--primary-purple)] text-white':
            activeTab === 'workspaces' && !themeService.isDarkMode(),
          'bg-[#00c39a] text-white':
            activeTab === 'workspaces' && themeService.isDarkMode(),
          'bg-[var(--hover-blue-gray)] text-[var(--text-dark)]':
            activeTab !== 'workspaces' && !themeService.isDarkMode(),
          'bg-[#3a3a45] text-gray-300':
            activeTab !== 'workspaces' && themeService.isDarkMode()
        }"
      >
        Workspaces
      </button>
    </div>

    <p
      class="text-sm"
      [ngClass]="{
        'text-gray-300': themeService.isDarkMode(),
        'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
      }"
    >
      {{
        activeTab === "agents"
          ? "Select an agent to start chatting"
          : "Choose a workspace to work in"
      }}
    </p>
  </div>

  <!-- AGENTS TAB CONTENT -->
  <div *ngIf="activeTab === 'agents'">
    <!-- Loading State for Agents -->
    <div *ngIf="isLoadingAgents" class="flex items-center justify-center py-8">
      <div class="flex items-center gap-2">
        <div
          class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin"
        ></div>
        <span
          class="text-sm"
          [ngClass]="{
            'text-gray-300': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }"
        >
          Loading agents...
        </span>
      </div>
    </div>

    <!-- Error State for Agents -->
    <div *ngIf="agentsError && !isLoadingAgents" class="text-center py-8">
      <div class="mb-4">
        <i class="ri-error-warning-line text-3xl text-red-500"></i>
      </div>
      <p class="text-sm text-red-500 mb-4">{{ agentsError }}</p>
      <button
        (click)="retryLoadAgents()"
        class="px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-[var(--secondary-purple)] transition-colors"
      >
        Retry
      </button>
    </div>

    <!-- Agent List -->
    <div
      *ngIf="!isLoadingAgents && !agentsError && agents.length > 0"
      class="space-y-3"
    >
      @for (agentName of agents; track $index) {
      <div
        (click)="navigateToAgentChat(agentName)"
        class="agent-card p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md"
        [ngClass]="{
          'bg-[#3a3a45] border-[#4a4a55] hover:border-[#00c39a] hover:bg-[#404050]':
            themeService.isDarkMode(),
          'bg-[var(--background-white)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)] hover:bg-[var(--background-light-gray)]':
            !themeService.isDarkMode()
        }"
      >
        <!-- Agent Icon and Name -->
        <div class="flex items-center gap-3">
          <div
            class="w-10 h-10 rounded-full flex items-center justify-center"
            [ngClass]="{
              'bg-[#00c39a]': themeService.isDarkMode(),
              'bg-[var(--primary-purple)]': !themeService.isDarkMode()
            }"
          >
            <i class="ri-robot-line text-white text-lg"></i>
          </div>

          <div class="flex-1">
            <h3
              class="font-semibold text-base"
              [ngClass]="{
                'text-white': themeService.isDarkMode(),
                'text-[var(--text-dark)]': !themeService.isDarkMode()
              }"
            >
              {{ formatAgentName(agentName) }}
            </h3>
          </div>

          <!-- Arrow Icon -->
          <div
            class="text-lg"
            [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"
          >
            <i class="ri-arrow-right-s-line"></i>
          </div>
        </div>
      </div>
      }
    </div>

    <!-- Empty State for Agents -->
    <div
      *ngIf="!isLoadingAgents && !agentsError && agents.length === 0"
      class="text-center py-8"
    >
      <div class="mb-4">
        <i
          class="ri-robot-line text-4xl"
          [ngClass]="{
            'text-gray-500': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }"
        ></i>
      </div>
      <p
        class="text-sm"
        [ngClass]="{
          'text-gray-400': themeService.isDarkMode(),
          'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
        }"
      >
        No agents available
      </p>
      <button
        (click)="retryLoadAgents()"
        class="mt-4 px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-[var(--secondary-purple)] transition-colors"
      >
        Refresh
      </button>
    </div>
  </div>

  <!-- WORKSPACES TAB CONTENT -->
  <div *ngIf="activeTab === 'workspaces'">
    <!-- Loading State for Workspaces -->
    <div
      *ngIf="isLoadingWorkspaces"
      class="flex items-center justify-center py-8"
    >
      <div class="flex items-center gap-2">
        <div
          class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin"
        ></div>
        <span
          class="text-sm"
          [ngClass]="{
            'text-gray-300': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }"
        >
          Loading workspaces...
        </span>
      </div>
    </div>

    <!-- Error State for Workspaces -->
    <div
      *ngIf="workspacesError && !isLoadingWorkspaces"
      class="text-center py-8"
    >
      <div class="mb-4">
        <i class="ri-error-warning-line text-3xl text-red-500"></i>
      </div>
      <p class="text-sm text-red-500 mb-4">{{ workspacesError }}</p>
      <button
        (click)="retryLoadWorkspaces()"
        class="px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-[var(--secondary-purple)] transition-colors"
      >
        Retry
      </button>
    </div>

    <!-- Workspace List -->
    <div
      *ngIf="!isLoadingWorkspaces && !workspacesError && workspaces.length > 0"
      class="space-y-1"
    >
      @for (workspace of workspaces; track workspace.id) {
      <div
        (click)="navigateToWorkspace(workspace)"
        class="workspace-list-item flex items-center gap-3 px-3 py-2 rounded-md cursor-pointer transition-all duration-200"
        [ngClass]="{
          'hover:bg-[#404050] text-gray-300 hover:text-white': themeService.isDarkMode(),
          'hover:bg-[var(--hover-blue-gray)] text-[var(--text-dark)] hover:text-[var(--text-dark)]': !themeService.isDarkMode()
        }"
      >
        <!-- Workspace Icon -->
        <div
          class="w-6 h-6 rounded flex items-center justify-center flex-shrink-0"
          [ngClass]="{
            'bg-[#00c39a]': themeService.isDarkMode(),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode()
          }"
        >
          <i class="ri-building-line text-white text-sm"></i>
        </div>

        <!-- Workspace Name -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium truncate">
              {{ workspace.title }}
            </span>
          </div>
        </div>

        <!-- Arrow Icon -->
        <div class="flex-shrink-0 opacity-60">
          <i class="ri-arrow-right-s-line text-sm"></i>
        </div>
      </div>
      }
    </div>

    <!-- Empty State for Workspaces -->
    <div
      *ngIf="
        !isLoadingWorkspaces && !workspacesError && workspaces.length === 0
      "
      class="text-center py-8"
    >
      <div class="">
        <i
          class="ri-building-line text-4xl"
          [ngClass]="{
            'text-gray-500': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }"
        ></i>
      </div>
      <p
        class="text-sm"
        [ngClass]="{
          'text-gray-400': themeService.isDarkMode(),
          'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
        }"
      >
        No workspaces available
      </p>
      <button
        (click)="retryLoadWorkspaces()"
        class="mt-4 px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-[var(--secondary-purple)] transition-colors"
      >
        Refresh
      </button>
    </div>
  </div>
</div>
