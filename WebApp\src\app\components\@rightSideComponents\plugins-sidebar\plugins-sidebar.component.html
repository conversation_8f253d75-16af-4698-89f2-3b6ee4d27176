<!-- Plugin Sidebar - Slides in from the right -->
<div *ngIf="showSidebar"
  class="flex-1 w-full h-full overflow-y-auto transition-all duration-300 ease-in-out"
  [ngClass]="{
    'translate-x-0': showSidebar,
    'translate-x-full': !showSidebar,
    'bg-[#2b2b33] text-white border-l border-[#3a3a45]': themeService.isDarkMode(),
    'bg-[var(--background-white)] text-[var(--text-dark)] border-l border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
  }">

  <!-- Plugin Sidebar Header - Sticky -->
  <div
    class="flex items-center justify-between p-4 sticky top-0 z-10 shadow-sm backdrop-blur-sm transition-all duration-200"
    [ngClass]="{
      'bg-[#2b2b33] border-b border-[#3a3a45]': themeService.isDarkMode(),
      'bg-[var(--background-white)] border-b border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
    }">
    <h3 class="font-semibold flex items-center gap-2"
        [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
      <i class="ri-plug-line" [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
      <span>{{pluginSidebarTitle}}</span>
    </h3>
    <button (click)="onCloseSidebar()"
      [ngClass]="{
        'text-gray-300  hover:bg-[#3a3a45]': themeService.isDarkMode(),
        'text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] hover:bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()
      }"
      class="transition-colors p-1 rounded-full">
      <i class="ri-close-line text-xl"></i>
    </button>
  </div>

  <!-- Plugin Content -->
  <div class="p-4">
    <!-- Agent Info Section -->
    <div *ngIf="selectedAgent" class="mb-6">
      <div class="flex items-center gap-2 mb-2">
        <i class="ri-user-2-line"
           [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
        <h4 class="font-medium"
            [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
          {{selectedAgent}}
        </h4>
      </div>
      <p class="text-xs"
         [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        Plugins connected to this agent
      </p>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center animate-pulse"
           [ngClass]="{
             'bg-[#3a3a45]': themeService.isDarkMode(),
             'bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()
           }">
        <i class="ri-loader-4-line text-2xl animate-spin"
           [ngClass]="{
             'text-[#00c39a]': themeService.isDarkMode(),
             'text-[var(--primary-purple)]': !themeService.isDarkMode()
           }">
        </i>
      </div>
      <p class="font-medium"
         [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
        Loading Plugins...
      </p>
      <p class="text-xs mt-1"
         [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        Fetching plugins for {{selectedAgent}}
      </p>
    </div>

    <!-- Error State -->
    <div *ngIf="hasError && !isLoading" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
           [ngClass]="{
             'bg-red-900': themeService.isDarkMode(),
             'bg-red-100': !themeService.isDarkMode()
           }">
        <i class="ri-error-warning-line text-2xl"
           [ngClass]="{
             'text-red-300': themeService.isDarkMode(),
             'text-red-600': !themeService.isDarkMode()
           }">
        </i>
      </div>
      <h4 class="font-medium mb-2"
          [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
        Failed to Load Plugins
      </h4>
      <p class="text-sm"
         [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        {{errorMessage}}
      </p>
      <p class="text-xs mt-2"
         [ngClass]="{'text-gray-400': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        Showing demo data instead
      </p>
    </div>

    <!-- Plugin List -->
    <div *ngIf="agentPlugins.length > 0 && !isLoading" class="space-y-4">
      <div *ngFor="let plugin of agentPlugins" (click)="selectPlugin(plugin)"
        class="p-4 rounded-lg shadow-sm transition-all cursor-pointer plugin-card group"
        [ngClass]="{
          'bg-[#3a3a45] border border-[#4a4a55] hover:border-[#00c39a]': themeService.isDarkMode(),
          'bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)]': !themeService.isDarkMode()
        }">

        <!-- Plugin Header -->
        <div class="flex items-start justify-between mb-3">
          <div class="flex items-center gap-3 flex-1 min-w-0">
            <!-- Plugin Icon -->
            <div class="flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center"
                 [ngClass]="{
                   'bg-[#1E1E1E] text-white': themeService.isDarkMode(),
                   'bg-[var(--hover-blue-gray)] text-[var(--text-dark)]': !themeService.isDarkMode()
                 }">
              <i class="ri-plug-line text-lg"></i>
            </div>

            <!-- Plugin Name -->
            <div class="flex-1 min-w-0">
              <h3 class="font-semibold plugin-name truncate"
                  [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
                {{plugin.pluginName}}
              </h3>
              <p class="text-xs mt-1"
                 [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
                {{getPluginDescription(plugin.functions)}}
              </p>
            </div>
          </div>

          <!-- Plugin Type Badge -->
          <span class="text-xs px-2 py-1 rounded-full font-medium flex-shrink-0 ml-2"
                [ngClass]="getPluginTypeBadgeClass(plugin.type)">
            {{plugin.type || 'Unknown'}}
          </span>
        </div>

        <!-- Plugin Functions Preview -->
        <div *ngIf="getFunctionList(plugin.functions).length > 0" class="mb-3">
          <div class="flex flex-wrap gap-1">
            <span *ngFor="let func of getFunctionList(plugin.functions).slice(0, 3)"
                  class="text-xs px-2 py-1 rounded-md"
                  [ngClass]="{
                    'bg-[#1E1E1E] text-gray-300': themeService.isDarkMode(),
                    'bg-[var(--background-light-gray)] text-[var(--text-dark)]': !themeService.isDarkMode()
                  }">
              {{func}}
            </span>
            <span *ngIf="getFunctionList(plugin.functions).length > 3"
                  class="text-xs px-2 py-1 rounded-md"
                  [ngClass]="{
                    'bg-[#1E1E1E] text-gray-400': themeService.isDarkMode(),
                    'bg-[var(--background-light-gray)] text-[var(--text-medium-gray)]': !themeService.isDarkMode()
                  }">
              +{{getFunctionList(plugin.functions).length - 3}} more
            </span>
          </div>
        </div>

        <!-- Plugin URL (if available) -->
        <div *ngIf="plugin.url" class="flex items-center gap-2 text-xs"
             [ngClass]="{'text-gray-400': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
          <i class="ri-external-link-line"></i>
          <span class="truncate">{{plugin.url}}</span>
        </div>

        <!-- Hover Effect Arrow -->
        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <i class="ri-arrow-right-s-line text-lg"
             [ngClass]="{
               'text-gray-400 group-hover:text-[#00c39a]': themeService.isDarkMode(),
               'text-[var(--text-medium-gray)] group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
             }"></i>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="agentPlugins.length === 0 && !isLoading && !hasError" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
           [ngClass]="{
             'bg-[#3a3a45]': themeService.isDarkMode(),
             'bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()
           }">
        <i class="ri-plug-line text-2xl"
           [ngClass]="{
             'text-gray-400': themeService.isDarkMode(),
             'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
           }">
        </i>
      </div>
      <h4 class="font-medium mb-2"
          [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
        No Plugins Connected
      </h4>
      <p class="text-sm"
         [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        <span *ngIf="selectedAgent">{{selectedAgent}} doesn't have any plugins configured.</span>
        <span *ngIf="!selectedAgent">Select an agent to view its plugins.</span>
      </p>
    </div>
  </div>
</div>
